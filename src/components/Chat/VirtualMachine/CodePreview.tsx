import React, { useState, useRef, useEffect } from 'react';
import { Button, Tabs } from 'antd';
import { EyeOutlined, CodeOutlined } from '@ant-design/icons';
import CodeRenderer from './CodeRenderer';
import styles from './CodePreview.module.css';

interface CodePreviewProps {
  codeType: 'html' | 'svg';
  content: string;
  title?: string;
}

const CodePreview: React.FC<CodePreviewProps> = ({
  codeType,
  content,
  title
}) => {
  const [activeTab, setActiveTab] = useState<string>('preview');
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const lastContentRef = useRef<string>(''); // 用于跟踪内容变化
  const isInitializedRef = useRef<boolean>(false); // 用于跟踪是否已初始化

  // 清理代码内容
  const cleanCode = (code: string): string => {
    // 移除markdown代码块标记
    return code
      .replace(/^```(html|svg|xml)?\\s*/i, '')
      .replace(/```\\s*$/, '')
      .trim();
  };

  const cleanedContent = cleanCode(content);

  // 更新预览
  const updatePreview = () => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    // 如果内容没有变化，不需要重新渲染
    if (lastContentRef.current === cleanedContent && isInitializedRef.current) {
      return;
    }

    try {
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) return;

      lastContentRef.current = cleanedContent; // 更新最后的内容
      isInitializedRef.current = true;

      let htmlContent = '';
      
      if (codeType === 'html') {
        // 检查是否包含echarts相关代码
        const hasEcharts = /echarts|chart/i.test(cleanedContent);
        
        // HTML代码处理
        htmlContent = `
          <!DOCTYPE html>
          <html style="height: 100%; overflow: auto;">
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <style>
                html, body {
                  margin: 0;
                  padding: 0;
                  height: 100%;
                  overflow: hidden !important;
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                  background-color: #f8fafc;
                }
                .slide-container {
                  width: 1280px;
                  min-height: 720px;
                  margin: 0 auto;
                  overflow: hidden;
                  background-color: white;
                  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                  position: relative;
                }
                .gradient-bg {
                  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
                }
                /* 确保echarts容器有合适的尺寸 */
                [id*="chart"], [class*="chart"], .echarts-container {
                  width: 100%;
                  height: 400px;
                  min-height: 300px;
                }
                * {
                  box-sizing: border-box;
                }
              </style>
              ${hasEcharts ? '<script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.6.0/echarts.min.js"></script>' : ''}
              <script>
                let echartsInitialized = false;
                let chartInstances = new Map(); // 存储图表实例，避免重复创建
                
                // ECharts加载逻辑
                ${hasEcharts ? `
                function initECharts() {
                  if (echartsInitialized || typeof echarts === 'undefined') return;
                  echartsInitialized = true;
                  
                  console.log('ECharts初始化开始');
                  
                  // 执行用户脚本
                  const scripts = document.querySelectorAll('script[type="application/javascript"], script:not([type]), script[type="text/javascript"]');
                  scripts.forEach((script, index) => {
                    if (script.src) return; // 跳过外部脚本
                    
                    try {
                      // 包装脚本执行，防止重复创建图表
                      const scriptContent = script.textContent;
                      if (scriptContent && !scriptContent.includes('echarts.init')) {
                        eval(scriptContent);
                      } else if (scriptContent.includes('echarts.init')) {
                        // 检查是否已经创建了图表
                        const containerMatch = scriptContent.match(/document\\.getElementById\\(['"]([^'"]+)['"]\\)/);
                        if (containerMatch && !chartInstances.has(containerMatch[1])) {
                          eval(scriptContent);
                        }
                      }
                    } catch (e) {
                      console.error('Script execution error:', e);
                    }
                  });
                  
                  // 确保所有echarts容器都有正确的尺寸
                  setTimeout(() => {
                    const chartContainers = document.querySelectorAll('[id*="chart"], [class*="chart"], .echarts-container');
                    chartContainers.forEach((container, index) => {
                      if (container instanceof HTMLElement && !container.style.width) {
                        container.style.width = '100%';
                        container.style.height = container.style.height || '400px';
                        container.style.display = 'block';
                        
                        if (!container.id) {
                          container.id = 'chart-' + index;
                        }
                      }
                    });
                  }, 100);
                }
                
                // 等待ECharts加载完成
                if (typeof echarts !== 'undefined') {
                  setTimeout(initECharts, 100);
                } else {
                  window.addEventListener('load', function() {
                    setTimeout(initECharts, 200);
                  });
                }
                ` : `
                // 页面不包含echarts，直接执行脚本
                document.addEventListener('DOMContentLoaded', function() {
                  const scripts = document.querySelectorAll('script[type="application/javascript"], script:not([type]), script[type="text/javascript"]');
                  scripts.forEach((script) => {
                    if (script.src) return;
                    try {
                      eval(script.textContent);
                    } catch (e) {
                      console.error('Script execution error:', e);
                    }
                  });
                });
                `}
              </script>
            </head>
            <body>
              ${cleanedContent}
            </body>
          </html>
        `;
      } else if (codeType === 'svg') {
        // SVG代码处理
        htmlContent = `
          <!DOCTYPE html>
          <html style="height: 100%; overflow: auto;">
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <style>
                html, body {
                  margin: 0;
                  padding: 16px;
                  height: 100%;
                  overflow: auto !important;
                  background-color: #f8f9fa;
                }
                body {
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                  align-items: center;
                  min-height: 100%;
                  padding-bottom: 40px;
                }
                .svg-container {
                  margin-top: 20px;
                  text-align: center;
                  transform-origin: top center;
                }
                svg {
                  max-width: 100%;
                  width: auto;
                  height: auto;
                  display: block;
                  overflow: visible;
                }
                * {
                  box-sizing: border-box;
                }
              </style>
            </head>
            <body>
              <div class="svg-container">
                ${cleanedContent}
              </div>
            </body>
          </html>
        `;
      }

      iframeDoc.open();
      iframeDoc.write(htmlContent);
      iframeDoc.close();

    } catch (error) {
      console.error('预览渲染错误:', error);
      
      // 显示错误信息
      try {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
        if (iframeDoc) {
          iframeDoc.open();
          iframeDoc.write(`
            <!DOCTYPE html>
            <html>
              <head>
                <style>
                  body {
                    font-family: Arial, sans-serif;
                    padding: 20px;
                    color: #d32f2f;
                    background-color: #ffebee;
                  }
                  .error-container {
                    border: 1px solid #d32f2f;
                    border-radius: 4px;
                    padding: 16px;
                    margin-bottom: 20px;
                  }
                  pre {
                    background: #f8f8f8;
                    padding: 10px;
                    border-radius: 4px;
                    overflow: auto;
                    font-size: 14px;
                  }
                </style>
              </head>
              <body>
                <div class="error-container">
                  <h3>渲染错误</h3>
                  <p>${error instanceof Error ? error.message : String(error)}</p>
                </div>
                <h4>原始代码：</h4>
                <pre>${cleanedContent.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
              </body>
            </html>
          `);
          iframeDoc.close();
        }
      } catch (e) {
        console.error('显示错误信息失败:', e);
      }
    }
  };

  // 当切换到预览模式时更新预览，但避免重复渲染
  useEffect(() => {
    if (activeTab === 'preview') {
      // 使用setTimeout确保iframe已经渲染
      const timer = setTimeout(updatePreview, 100);
      return () => clearTimeout(timer);
    }
  }, [activeTab]); // 只依赖activeTab，不依赖content

  // 当内容真正改变时才更新
  useEffect(() => {
    if (activeTab === 'preview' && cleanedContent !== lastContentRef.current) {
      const timer = setTimeout(updatePreview, 150);
      return () => clearTimeout(timer);
    }
  }, [cleanedContent, activeTab]);

  // 渲染预览内容 - 使用iframe
  const renderPreview = () => {
    return (
      <div className={styles.previewContainer}>
        <iframe
          ref={iframeRef}
          className={styles.previewIframe}
          title={`${codeType} preview`}
          sandbox="allow-scripts allow-same-origin"
        />
      </div>
    );
  };

  // 渲染代码内容
  const renderCode = () => {
    return (
      <div className={styles.codeContainer}>
        <div className={styles.codeRendererWrapper}>
          <CodeRenderer 
            content={cleanedContent}
            language={codeType}
          />
        </div>
      </div>
    );
  };

  const tabItems = [
    {
      key: 'preview',
      label: (
        <span>
          <EyeOutlined />
          预览
        </span>
      ),
      children: renderPreview(),
    },
    {
      key: 'code',
      label: (
        <span>
          <CodeOutlined />
          代码
        </span>
      ),
      children: renderCode(),
    },
  ];

  return (
    <div className={styles.codePreview}>
      <div className={styles.header}>
        <span className={styles.title}>
          {title || `${codeType.toUpperCase()} 代码`}
        </span>
        <Button
          size="small"
          onClick={() => {
            navigator.clipboard.writeText(cleanedContent);
          }}
        >
          复制代码
        </Button>
      </div>
      
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        className={styles.tabs}
      />
    </div>
  );
};

export default CodePreview; 