.virtualMachinePanel {
  position: fixed;
  top: 0;
  right: 0;
  width: calc(50vw - 8px);
  height: 100vh;
  background: #f8f9fa;
  border-left: 1px solid #e8e8e8;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
}

.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #1890ff;
  color: white;
  font-weight: 600;
  border-bottom: 1px solid #e8e8e8;
  
  .panelTitle {
    font-size: 16px;
  }
  
  .closeButton {
    color: white;
    border: none;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }
  }
}

.panelContent {
  flex: 1;
  overflow: hidden; /* 改为hidden，让内部组件管理滚动 */
  background: white;
  min-height: 0; /* 关键：允许flex item缩小 */
  display: flex;
  flex-direction: column;
}

.progressBar {
  padding: 12px 16px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
}

.progressInfo {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
  gap: 16px;
}

.progressText {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.backToLatestButton {
  font-size: 12px;
  padding: 0;
  height: auto;
  color: #1890ff;
}

.backToLatestButton:hover {
  color: #40a9ff;
}

.progressTrack {
  width: 100%;
  height: 4px;
  background: #e8e8e8;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.progressFill::after {
  content: '';
  position: absolute;
  top: 0;
  right: -2px;
  width: 2px;
  height: 100%;
  background: #389e0d;
  border-radius: 0 2px 2px 0;
}

/* 搜索界面样式 */
.searchInterface {
  padding: 0;
  
  .searchHeader {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    background: #e6f7ff;
    border-radius: 8px;
    
    .searchIcon {
      font-size: 24px;
      color: #1890ff;
    }
    
    .searchTitle {
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
    }
  }
  
  .searchQuery {
    margin-bottom: 20px;
    padding: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    font-size: 14px;
  }
  
  .searchResults {
    .resultHeader {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #333;
    }
    
    .resultContent {
      background: white;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      max-height: 400px;
      overflow-y: auto;
    }
  }
}

/* 浏览界面样式 */
.viewInterface {
  padding: 0;
  
  .viewHeader {
    margin-bottom: 20px;
    padding: 16px;
    background: #f6ffed;
    border-radius: 8px;
    
    .viewTitle {
      font-size: 18px;
      font-weight: 600;
      color: #52c41a;
    }
  }
  
  .urlBar {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    padding: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    
    .urlInput {
      flex: 1;
    }
  }
  
  .pageContent {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-height: 400px;
    
    .loadingPage {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 400px;
      gap: 20px;
      
      p {
        color: #666;
        font-size: 16px;
        margin: 0;
      }
    }
    
    .simulatedPage {
      .pageHeader {
        padding: 16px;
        background: #fafafa;
        border-bottom: 1px solid #e8e8e8;
        border-radius: 8px 8px 0 0;
        
        .pageTitle {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }
      
      .pageBody {
        padding: 16px;
        max-height: 400px;
        overflow-y: auto;
      }
    }
  }
}

/* 默认界面样式 */
.defaultInterface {
  padding: 0;
  
  .toolQuery {
    margin-bottom: 20px;
    padding: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    font-size: 14px;
  }
  
  .toolResult {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-height: 400px;
    overflow-y: auto;
  }
}

/* 聚合界面样式 */
.aggregationInterface {
  padding: 0;
  
  .aggregationHeader {
    margin-bottom: 20px;
    padding: 16px;
    background: #f0f5ff;
    border-radius: 8px;
    
    .aggregationTitle {
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
    }
  }
  
  .aggregationQuery {
    margin-bottom: 20px;
    padding: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    font-size: 14px;
  }
  
  .aggregationResult {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-height: 400px;
    overflow-y: auto;
  }
}

/* 反思界面样式 */
.reflectionInterface {
  padding: 0;
  
  /* .reflectionHeader {
    margin-bottom: 20px;
    padding: 16px;
    background: #fff2e8;
    border-radius: 8px;
    
    .reflectionTitle {
      font-size: 18px;
      font-weight: 600;
      color: #fa8c16;
    }
  } */
  
  .reflectionQuery {
    margin-bottom: 20px;
    padding: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    font-size: 14px;
  }
  
  .reflectionResult {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-height: 400px;
    overflow-y: auto;
  }
}

/* JSON结果样式 */
.jsonResult {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #24292e;
  margin: 0;
}

/* 响应式设计 */
/* 响应式设计 */
@media (max-width: 768px) {
  .virtualMachinePanel {
    width: 100%;
    z-index: 1001;
  }
  
  .panelHeader {
    padding: 12px 16px;
  }
  
  .panelTitle {
    font-size: 14px;
  }
  
  .progressBar {
    padding: 8px 12px;
  }
  
  .progressText {
    font-size: 11px;
  }
  
  .searchInterface,
  .viewInterface,
  .defaultInterface,
  .aggregationInterface,
  .reflectionInterface {
    padding: 0;
  }
  
  .urlBar {
    flex-direction: column;
    gap: 8px;
  }
}

/* 平板设备适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .virtualMachinePanel {
    width: calc(60vw - 8px);
  }
}

/* 代码渲染器样式 */
.pageCodeRenderer,
.aggregationCodeRenderer,
.reflectionCodeRenderer,
.defaultCodeRenderer,
.reasoningCodeRenderer {
  height: 100%;
  width: 100%;
  overflow: auto;
}

/* CodePreview 在虚拟机中的样式 */
.codePreviewContainer {
  height: 100%;
  width: 100%;
  min-height: 700px; /* 增加最小高度 */
  display: flex;
  flex-direction: column;
}



/* 修复其他界面的溢出问题 */
.defaultInterface .toolResult,
.aggregationInterface .aggregationResult,
.reflectionInterface .reflectionResult {
  max-height: 400px;
  overflow: auto;
}

.viewInterface .pageContent .simulatedPage .pageBody {
  max-height: 400px;
  overflow: auto;
}

/* 页面导航样式 */
.pageNavigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 6px 6px 0 0;
}

.pageInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pageTitle {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.pageControls {
  display: flex;
  gap: 8px;
}

/* 多页面连续显示样式 */
.multiPageContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.multiPageHeader {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.multiPageTitle {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.multiPageContent {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
}

.pageSection {
  border-bottom: 2px solid #e9ecef;
  background: white;
}

.pageSection:last-child {
  border-bottom: none;
}

.pageSectionHeader {
  padding: 12px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pageSectionTitle {
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  display: flex;
  align-items: center;
}

.pageSectionTitle::before {
  content: "📄";
  margin-right: 8px;
}

/* 调整页面内容的布局 */
.pageSection .codePreviewContainer {
  border: none;
  border-radius: 0;
}

/* 确保滚动条样式 */
.multiPageContent::-webkit-scrollbar {
  width: 8px;
}

.multiPageContent::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.multiPageContent::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.multiPageContent::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
} 