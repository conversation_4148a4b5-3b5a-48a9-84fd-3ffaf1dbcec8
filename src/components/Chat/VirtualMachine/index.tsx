import React, { useState, useEffect } from 'react';
import { Input, <PERSON><PERSON>, Spin } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import MarkdownRenderer from '../MarkdownRenderer';
import { SearchResults, FileViewer } from '../action_components';
import CodePreview from './CodePreview';
import CodeRenderer from './CodeRenderer';
import styles from './index.module.css';

interface VirtualMachineProps {
  visible: boolean;
  onClose: () => void;
  actionName: string;
  toolResult: any;
  toolInput: string;
  eventId: string;
  toolEvents: Record<string, any>;
  onJumpToLatest?: () => void;
  groupedEvents?: Array<{
    eventId: string;
    toolResult: any;
    toolInput: string;
    created_at: string;
  }>;
  currentPageIndex?: number;
  query?: string;
}

const SEARCH_ACTION_NAME = "search";
const VIEW_ACTION_NAME = 'view';
const AGGREGATION_ACTION_NAME = 'aggregation';
const REFLECTION_ACTION_NAME = 'reflection';
const HTML_ACTION_NAME = 'html';
const SVG_ACTION_NAME = 'svg';
const REASONING_CONTENT_ACTION_NAME = 'reasoning_content';

const VirtualMachine: React.FC<VirtualMachineProps> = ({
  visible,
  onClose,
  actionName,
  toolResult,
  toolInput,
  eventId,
  toolEvents,
  onJumpToLatest,
  groupedEvents,
  currentPageIndex,
  query
}) => {
  const [searchUrl, setSearchUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [viewMode, setViewMode] = useState<'search' | 'file' | 'web'>('search');
  const [selectedFile, setSelectedFile] = useState<{url: string, title: string} | null>(null);

  // ESC键关闭功能
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 只有当虚拟机显示时才响应ESC键
      if (visible && event.key === 'Escape') {
        event.preventDefault();
        onClose();
      }
    };

    // 只有当虚拟机显示时才添加事件监听
    if (visible) {
      document.addEventListener('keydown', handleKeyDown);
    }

    // 清理事件监听
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [visible, onClose]);

  // 获取当前页面的数据（现在主要用于单页面情况）
  const getCurrentPageData = () => {
    return {
      toolResult,
      toolInput,
      eventId,
      totalPages: 1,
      currentPage: 1
    };
  };

  // 计算进度条相关数据
  const getProgressData = () => {
    const toolEventsList = Object.values(toolEvents).filter(event => 
      event.event_type === 'tool_call' // 只包含 tool_call 事件，排除 chat.completion 事件
    ).sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    
    const currentIndex = toolEventsList.findIndex(event => event.id === eventId);
    const totalEvents = toolEventsList.length;
    const progressPercent = totalEvents > 0 ? ((currentIndex + 1) / totalEvents) * 100 : 0;
    const isLatest = currentIndex === totalEvents - 1;
    
    // 调试信息
    console.log('Progress Debug:', {
      eventId,
      currentIndex,
      totalEvents,
      isLatest,
      hasCallback: !!onJumpToLatest,
      shouldShowButton: !isLatest && !!onJumpToLatest,
      shouldShowProgressBar: totalEvents > 1
    });
    
    return {
      currentIndex: currentIndex + 1,
      totalEvents,
      progressPercent,
      toolEventsList,
      isLatest
    };
  };

  const { currentIndex, totalEvents, progressPercent, isLatest } = getProgressData();

  // 解析搜索结果数据
  const parseSearchData = () => {
    if (!toolResult) return [];
    
    try {
      // 如果toolResult是数组
      if (Array.isArray(toolResult)) {
        const firstItem = toolResult[0];
        if (firstItem && firstItem.tool_result && Array.isArray(firstItem.tool_result)) {
          return firstItem.tool_result;
        }
      }
      
      // 如果toolResult直接包含tool_result
      if (toolResult.tool_result && Array.isArray(toolResult.tool_result)) {
        return toolResult.tool_result;
      }
      
      // 如果toolResult本身就是搜索结果数组
      if (toolResult.length && toolResult[0].title) {
        return toolResult;
      }
      
      return [];
    } catch (error) {
      console.error('解析搜索数据失败:', error);
      return [];
    }
  };

  // 处理文件点击
  const handleFileClick = (fileUrl: string, title: string) => {
    setSelectedFile({ url: fileUrl, title });
    setViewMode('file');
  };

  // 处理网页点击
  const handleUrlClick = (url: string, title: string) => {
    setSelectedFile({ url, title });
    setViewMode('web');
  };

  // 返回搜索结果
  const handleBackToSearch = () => {
    setViewMode('search');
    setSelectedFile(null);
  };

  // 根据工具名称渲染不同内容
  const renderContent = () => {
    switch (actionName) {
      case SEARCH_ACTION_NAME:
        const searchData = parseSearchData();
        
        if (viewMode === 'file' && selectedFile) {
          return (
            <FileViewer
              fileUrl={selectedFile.url}
              title={selectedFile.title}
              onBack={handleBackToSearch}
            />
          );
        }
        
        if (viewMode === 'web' && selectedFile) {
          return (
            <FileViewer
              webUrl={selectedFile.url}
              title={selectedFile.title}
              onBack={handleBackToSearch}
            />
          );
        }
        
        return (
          <SearchResults
            data={searchData}
            query={toolInput}
            onFileClick={handleFileClick}
            onUrlClick={handleUrlClick}
          />
        );

      case VIEW_ACTION_NAME:
        return (
          <div className={styles.viewInterface}>
            <div className={styles.viewHeader}>
              <span className={styles.viewTitle}>网页浏览模拟器</span>
            </div>
            <div className={styles.urlBar}>
              <Input
                value={searchUrl || (typeof toolResult === 'string' ? toolResult : '')}
                onChange={(e) => setSearchUrl(e.target.value)}
                placeholder="输入URL地址"
                prefix="🌐"
                className={styles.urlInput}
              />
              <Button type="primary" onClick={() => setIsLoading(!isLoading)}>
                访问
              </Button>
            </div>
            <div className={styles.pageContent}>
              {isLoading ? (
                <div className={styles.loadingPage}>
                  <Spin size="large" />
                  <p>正在加载页面...</p>
                </div>
              ) : (
                <div className={styles.simulatedPage}>
                  <div className={styles.pageHeader}>
                    <div className={styles.pageTitle}>模拟页面内容</div>
                  </div>
                  <div className={styles.pageBody}>
                    {typeof toolResult === 'string' ? (
                      <CodeRenderer 
                        content={toolResult}
                        language="markdown"
                        className={styles.pageCodeRenderer}
                      />
                    ) : (
                      <CodeRenderer 
                        content={JSON.stringify(toolResult, null, 2)}
                        language="json"
                        className={styles.pageCodeRenderer}
                      />
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        );

      case AGGREGATION_ACTION_NAME:
        return (
          <div className={styles.aggregationInterface}>
            <div className={styles.aggregationHeader}>
              <span className={styles.aggregationTitle}>数据聚合结果</span>
            </div>
            <div className={styles.aggregationQuery}>
              <strong>聚合查询：</strong> {toolInput}
            </div>
            <div className={styles.aggregationResult}>
              {typeof toolResult === 'string' ? (
                <CodeRenderer 
                  content={toolResult}
                  language="markdown"
                  className={styles.aggregationCodeRenderer}
                />
              ) : (
                <CodeRenderer 
                  content={JSON.stringify(toolResult, null, 2)}
                  language="json"
                  className={styles.aggregationCodeRenderer}
                />
              )}
            </div>
          </div>
        );

      case REFLECTION_ACTION_NAME:
        return (
          <div className={styles.reflectionInterface}>
            {/* <div className={styles.reflectionHeader}>
              <span className={styles.reflectionTitle}>反思分析</span>
            </div> */}
            <div className={styles.reflectionQuery}>
              <strong>反思：</strong> {toolInput}
            </div>
            <div className={styles.reflectionResult}>
              {typeof toolResult === 'string' ? (
                <CodeRenderer 
                  content={toolResult}
                  language="markdown"
                  className={styles.reflectionCodeRenderer}
                />
              ) : (
                <CodeRenderer 
                  content={JSON.stringify(toolResult, null, 2)}
                  language="json"
                  className={styles.reflectionCodeRenderer}
                />
              )}
            </div>
          </div>
        );

      case HTML_ACTION_NAME:
        const htmlPageData = getCurrentPageData();
        
        // 如果有分组事件，显示所有页面
        if (groupedEvents && groupedEvents.length > 1) {
          return (
            <div className={styles.multiPageContainer}>
              <div className={styles.multiPageContent}>
                {groupedEvents.map((event, _) => (
                  <div key={event.eventId} className={styles.pageSection}>
                    <CodePreview
                      codeType="html"
                      content={typeof event.toolResult === 'string' ? event.toolResult : JSON.stringify(event.toolResult, null, 2)}
                      title={event.toolInput}
                    />
                  </div>
                ))}
              </div>
            </div>
          );
        }
        
        // 单页面显示（原有逻辑）
        return (
          <div className={styles.codePreviewContainer}>
            <CodePreview
              codeType="html"
              content={typeof htmlPageData.toolResult === 'string' ? htmlPageData.toolResult : JSON.stringify(htmlPageData.toolResult, null, 2)}
              title={htmlPageData.toolInput}
            />
          </div>
        );

      case SVG_ACTION_NAME:
        const svgPageData = getCurrentPageData();
        
        // 如果有分组事件，显示所有页面
        if (groupedEvents && groupedEvents.length > 1) {
          return (
            <div className={styles.multiPageContainer}>
              {/* <div className={styles.multiPageHeader}>
                <span className={styles.multiPageTitle}>
                  {query && `${query} - `}共 {groupedEvents.length} 个页面
                </span>
              </div> */}
              <div className={styles.multiPageContent}>
                {groupedEvents.map((event, _) => (
                  <div key={event.eventId} className={styles.pageSection}>
                    {/* <div className={styles.pageSectionHeader}>
                      <span className={styles.pageSectionTitle}>
                        页面 {index + 1} - {event.toolInput}
                      </span>
                    </div> */}
                    <CodePreview
                      codeType="svg"
                      content={typeof event.toolResult === 'string' ? event.toolResult : JSON.stringify(event.toolResult, null, 2)}
                      title={event.toolInput}
                    />
                  </div>
                ))}
              </div>
            </div>
          );
        }
        
        // 单页面显示（原有逻辑）
        return (
          <div className={styles.codePreviewContainer}>
            <CodePreview
              codeType="svg"
              content={typeof svgPageData.toolResult === 'string' ? svgPageData.toolResult : JSON.stringify(svgPageData.toolResult, null, 2)}
              title={svgPageData.toolInput}
            />
          </div>
        );

      default:
        return (
          <div className={styles.defaultInterface}>
            <div className={styles.toolQuery}>
              <strong>执行查询：</strong> {toolInput}
            </div>
            <div className={styles.toolResult}>
              {typeof toolResult === 'string' ? (
                <CodeRenderer 
                  content={toolResult}
                  language="markdown"
                  className={styles.defaultCodeRenderer}
                />
              ) : (
                <CodeRenderer 
                  content={JSON.stringify(toolResult, null, 2)}
                  language="json"
                  className={styles.defaultCodeRenderer}
                />
              )}
            </div>
          </div>
        );
    }
  };

  // 获取模态框标题
  const getModalTitle = () => {
    if (actionName === SEARCH_ACTION_NAME) {
      if (viewMode === 'file') {
        return '文件预览';
      } else if (viewMode === 'web') {
        return '网页预览';
      } else {
        return '搜索结果';
      }
    }
    if (actionName === AGGREGATION_ACTION_NAME) {
      return '数据聚合';
    }
    if (actionName === REFLECTION_ACTION_NAME) {
      return '反思分析';
    }
    if (actionName === HTML_ACTION_NAME) {
      const baseTitle = 'HTML 代码预览';
      if (groupedEvents && groupedEvents.length > 1) {
        return `${baseTitle} (共${groupedEvents.length}页)`;
      }
      return baseTitle;
    }
    if (actionName === SVG_ACTION_NAME) {
      const baseTitle = 'SVG 代码预览';
      if (groupedEvents && groupedEvents.length > 1) {
        return `${baseTitle} (共${groupedEvents.length}页)`;
      }
      return baseTitle;
    }
    return actionName;
  };

  if (!visible) return null;
  console.log(toolEvents,isLatest,onJumpToLatest,'onJumpToLatestonJumpToLatestonJumpToLatest')
  return (
    <div className={styles.virtualMachinePanel}>
      <div className={styles.panelHeader}>
        <span className={styles.panelTitle}>虚拟机 - {getModalTitle()}</span>
        <Button 
          type="text" 
          icon={<CloseOutlined />} 
          onClick={onClose}
          className={styles.closeButton}
        />
      </div>
      <div className={styles.panelContent}>
        {renderContent()}
      </div>
      {/* 进度条 */}
      {totalEvents > 1 && (
        <div className={styles.progressBar}>
          <div className={styles.progressInfo}>
            <span className={styles.progressText}>
              工具事件 {currentIndex} / {totalEvents}
            </span>
            {!isLatest && onJumpToLatest && (
              <Button 
                type="link" 
                size="small"
                onClick={onJumpToLatest}
                className={styles.backToLatestButton}
              >
                返回至最新进展
              </Button>
            )}
          </div>
          <div className={styles.progressTrack}>
            <div 
              className={styles.progressFill}
              style={{ width: `${progressPercent}%` }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default VirtualMachine; 