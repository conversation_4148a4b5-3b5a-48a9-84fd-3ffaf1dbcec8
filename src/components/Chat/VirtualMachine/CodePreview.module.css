.codePreview {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  min-height: 0 !important;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  flex-shrink: 0 !important;
}

.title {
  font-weight: 500;
  font-size: 14px;
  color: #262626;
}

.tabs {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  min-height: 0 !important;
  height: 100% !important;
}

.tabs :global(.ant-tabs) {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.tabs :global(.ant-tabs-content-holder) {
  flex: 1 !important;
  min-height: 0 !important;
  overflow: hidden !important;
  height: 100% !important;
}

.tabs :global(.ant-tabs-tabpane) {
  height: 100% !important;
  min-height: 0 !important;
}

/* 预览容器样式 */
.previewContainer {
  height: 100% !important;
  width: 100% !important;
  min-height: 0 !important;
  position: relative;
  background: #fff;
  padding: 0;
  border: none;
  display: flex !important;
  flex-direction: column !important;
}

.previewIframe {
  width: 1280px;
  height: 890px;
  transform: scale(0.58);
  transform-origin: 0 0;
  border: none;
  background: #fff;
  display: block;

}

/* 代码容器样式 */
.codeContainer {
  height: 100% !important;
  width: 100% !important;
  min-height: 500px !important; /* 增加最小高度 */
  overflow: hidden !important;
  background: #ffffff;
  display: flex !important;
  flex-direction: column !important;
}

.codeRendererWrapper {
  height: 100% !important;
  width: 100% !important;
  flex: 1 !important;
  min-height: 0 !important;
}

/* 预览样式 */
.htmlPreview,
.svgPreview {
  padding: 16px;
  height: 100%;
  overflow: auto;
  background: #ffffff;
}

.htmlContent,
.svgContent {
  width: 100%;
  height: 100%;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 16px;
  background: #ffffff;
  overflow: auto;
}

.svgContent {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 200px;
}

.svgContent svg {
  max-width: 100%;
  height: auto;
}

/* 错误信息样式 */
.errorMessage {
  padding: 16px;
  color: #d32f2f;
  background-color: #ffebee;
  border: 1px solid #d32f2f;
  border-radius: 4px;
  margin: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 8px 12px;
  }
  
  .title {
    font-size: 13px;
  }
  
  .htmlContent,
  .svgContent {
    padding: 12px;
  }
} 