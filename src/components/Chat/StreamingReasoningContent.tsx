import React, { useState, useEffect } from 'react';
import { DownOutlined, RightOutlined } from '@ant-design/icons';
import MarkdownRenderer from './MarkdownRenderer';
import styles from './MessageProcessor.module.css';

interface StreamingReasoningContentProps {
  content: string;
  isStreaming?: boolean;
  defaultExpanded?: boolean;
  onHtmlSvgCodeDetected?: (detectedBlocks: any[], isUserClick?: boolean) => void;
}

const StreamingReasoningContent: React.FC<StreamingReasoningContentProps> = ({
  content,
  isStreaming = false,
  defaultExpanded = false,
  onHtmlSvgCodeDetected
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [accumulatedContent, setAccumulatedContent] = useState('');

  // 直接使用传入的内容，不需要累加（累加在父组件中已经完成）
  useEffect(() => {
    setAccumulatedContent(content);
  }, [content]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={`${styles.reasoningContent} ${isStreaming ? styles.streaming : ''}`}>
      <div 
        className={styles.reasoningHeader}
        onClick={toggleExpanded}
      >
        <span className={styles.reasoningIcon}>
          {isExpanded ? <DownOutlined /> : <RightOutlined />}
        </span>
        <span className={styles.reasoningTitle}>
          🧠 推理过程
        </span>
        {isStreaming && (
          <span className={styles.streamingIndicator}>流式输出中...</span>
        )}
      </div>
      {isExpanded && (
        <div className={styles.reasoningBody}>
          <MarkdownRenderer
            content={accumulatedContent}
            onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
            isStreaming={isStreaming}
            role="ai"
          />
        </div>
      )}
    </div>
  );
};

export default StreamingReasoningContent;
