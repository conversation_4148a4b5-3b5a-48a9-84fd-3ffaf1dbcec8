import React, { useState } from 'react';

import { DownOutlined, RightOutlined } from '@ant-design/icons';
import MarkdownRenderer from './MarkdownRenderer';
import StreamingReasoningContent from './StreamingReasoningContent';
import StreamingLLMAnswer from './StreamingLLMAnswer';
import StreamingLLMContent from './StreamingLLMContent';
import styles from './MessageProcessor.module.css';

// 事件类型常量
const EVENT_TYPES = {
  TOOL_CALL: 'tool_call',
  CHAT_COMPLETION_CHUNK: 'chat.completion.chunk',
  CHAT_COMPLETION: 'chat.completion'
} as const;

// 虚拟机以及module.css也需要修改
// 工具名称常量
const ACTION_NAMES = {
  SEARCH: 'search',
  MCP: 'mcp',
  QUERY_EXPANSION: 'query_expansion',
  DEDUPLICATION: 'deduplication',
  SUMMARY: 'summary',
  VIEW: 'view',
  AGGREGATION: 'aggregation',
  REFLECTION: 'reflection',
  LLM: 'llm', // 新增 LLM 工具类型
  LLM_ANSWER: 'llm_answer', // 新增 LLM_ANSWER
  REASONING_CONTENT: 'reasoning_content', // 新增 REASONING_CONTENT
  HTML: 'html', // 新增 HTML action
  SVG: 'svg' // 新增 SVG action
} as const;

// 工具名称图标映射
const ACTION_NAME_ICONS = {
  [ACTION_NAMES.SEARCH]: '🔍',
  [ACTION_NAMES.MCP]: '🔧',
  [ACTION_NAMES.QUERY_EXPANSION]: '📝',
  [ACTION_NAMES.DEDUPLICATION]: '🔄',
  [ACTION_NAMES.SUMMARY]: '📋',
  [ACTION_NAMES.VIEW]: '👀',
  [ACTION_NAMES.AGGREGATION]: '📊',
  [ACTION_NAMES.REFLECTION]: '🤔',
  [ACTION_NAMES.LLM]: '🤖', // LLM 使用机器人图标
  [ACTION_NAMES.LLM_ANSWER]: '💬', // LLM_ANSWER 使用对话图标
  [ACTION_NAMES.REASONING_CONTENT]: '🧠', // REASONING_CONTENT 使用大脑图标
  [ACTION_NAMES.HTML]: '🌐', // HTML 使用网页图标
  [ACTION_NAMES.SVG]: '🎨', // SVG 使用画笔图标
  'default': '🛠️'
} as const;

interface MessageProcessorProps {
  content: string | any[]; // 可以是字符串或事件数组
  role: 'local' | 'ai';
  onHtmlSvgCodeDetected?: (detectedBlocks: any[], isUserClick?: boolean) => void;
  isStreaming?: boolean;
  toolEvents: Record<string, any>;
  onToolEventClick?: (eventId: string, actionName: string, toolResult: any, query: string) => void;
}

interface DbEvent {
  id: string;
  session_id: string;
  event_type: string;
  query: string;
  event_payload: {
    action_name: string;
    is_show?: boolean;
    content: {
      tool_result?: any;
      [key: string]: any;
    };
  };
  created_at: string;
  [key: string]: any;
}

const MessageProcessor: React.FC<MessageProcessorProps> = ({
  content,
  role,
  onHtmlSvgCodeDetected,
  isStreaming,
  toolEvents,
  onToolEventClick
}) => {
  const [orderedContent, setOrderedContent] = useState<JSX.Element[]>([]);
  // 改为存储展开状态的项目，默认reasoning都是折叠的
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());



  // 切换折叠状态
  const toggleCollapse = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  // HTML/SVG 开始结束标记检测
  const isHtmlStart = (content: string): boolean => {
    const trimmed = content.trim().toLowerCase();
    return trimmed.includes('```html') ||
           trimmed.includes('```<html') ||
           trimmed.includes('```<!doctype') ||
           trimmed.includes('<html') ||
           trimmed.includes('<!doctype');
  };

  const isHtmlEnd = (content: string): boolean => {
    return content.toLowerCase().includes('</html>');
  };

  const isSvgStart = (content: string): boolean => {
    return content.trim().toLowerCase().includes('```svg');
  };

  const isSvgEnd = (content: string): boolean => {
    return content.trim().endsWith('```');
  };

  React.useEffect(() => {
    processMessage();
  }, [content, toolEvents, expandedItems]);

  const processMessage = () => {
    if (role === 'local') {
      // 用户消息直接显示
      const textContent = typeof content === 'string' ? content : '';
      if (textContent) {
        setOrderedContent([
          <MarkdownRenderer
            key="local-content"
            content={textContent}
            onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
            isStreaming={isStreaming}
            role={role}
          />
        ]);
      } else {
        setOrderedContent([]);
      }
      return;
    }

    // 处理AI消息
    if (Array.isArray(content)) {
      // 如果content是事件数组，先按类型分组累加内容
      const contentGroups: {
        [key: string]: {
          type: string;
          events: DbEvent[];
          accumulatedContent: string;
        }
      } = {};

      // 分组事件 - 分别处理 CHAT_COMPLETION_CHUNK 和 CHAT_COMPLETION
      content.forEach((event: DbEvent) => {
        if (event.event_type === EVENT_TYPES.CHAT_COMPLETION_CHUNK || event.event_type === EVENT_TYPES.CHAT_COMPLETION) {
          const actionName = event.event_payload?.action_name;
          if (actionName && [ACTION_NAMES.REASONING_CONTENT, ACTION_NAMES.LLM_ANSWER, ACTION_NAMES.HTML, ACTION_NAMES.SVG, ACTION_NAMES.LLM].includes(actionName as any)) {
            // 为流式和静态内容创建不同的分组键
            const prefix = event.event_type === EVENT_TYPES.CHAT_COMPLETION_CHUNK ? 'streaming' : 'static';
            const groupKey = `${prefix}-${actionName}-${event.query || 'default'}`;

            if (!contentGroups[groupKey]) {
              contentGroups[groupKey] = {
                type: actionName,
                events: [],
                accumulatedContent: ''
              };
            }
            contentGroups[groupKey].events.push(event);

            // 累加内容
            const eventContent = event.event_payload?.content?.tool_result || event.event_payload?.content || '';
            let contentString = '';
            if (typeof eventContent === 'object' && eventContent !== null && 'tool_result' in eventContent) {
              contentString = typeof eventContent.tool_result === 'string' ? eventContent.tool_result : JSON.stringify(eventContent.tool_result, null, 2);
            } else if (typeof eventContent === 'string') {
              contentString = eventContent;
            } else {
              contentString = JSON.stringify(eventContent, null, 2);
            }
            contentGroups[groupKey].accumulatedContent += contentString;
          }
        }
      });

      // 现在按原始顺序处理事件，但使用累加的内容
      const orderedElements: JSX.Element[] = [];
      let accumulatedText = '';
      const processedGroups = new Set<string>();

      content.forEach((event: DbEvent, index: number) => {
        if (event.event_type === EVENT_TYPES.TOOL_CALL) {
          // 先添加之前累积的文本（如果有的话）
          if (accumulatedText.trim()) {
            orderedElements.push(
              <MarkdownRenderer
                key={`text-before-${index}`}
                content={accumulatedText}
                onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                isStreaming={isStreaming}
                role="ai"
              />
            );
            accumulatedText = '';
          }
          
          // 处理工具调用事件
          const { id, query, event_payload } = event;
          const actionName = event_payload?.action_name || 'unknown';
          const toolInput = event_payload?.content?.input || query;
          const toolResult = event_payload?.content?.tool_result || '';
          
          // 检查是否应该显示虚拟机
          const isShow = event_payload?.is_show !== false; // 默认为true，只有明确设置为false才不显示
          const hasContent = event_payload?.content && Object.keys(event_payload.content).length > 0;
          const canClickToVirtualMachine = isShow && hasContent;
          
          // 获取工具图标
          const toolIcon = ACTION_NAME_ICONS[actionName as keyof typeof ACTION_NAME_ICONS] || ACTION_NAME_ICONS.default;
          
          // 创建工具调用元素
          const toolCallElement = (
            <div
              key={`tool-${id}-${index}`}
              id={id}
              className={`${styles.toolCallItem} ${!canClickToVirtualMachine ? styles.toolCallItemDisabled : ''}`}
              data-tool={actionName}
              onClick={() => canClickToVirtualMachine && onToolEventClick?.(id, actionName, toolResult, toolInput)}
              style={{ cursor: canClickToVirtualMachine ? 'pointer' : 'default', marginBottom: '8px' }}
            >
              <div className={styles.toolCallHeader}>
                <span className={styles.toolIcon}>{toolIcon}</span>
                <span className={styles.actionName}>{actionName}</span>
                <span className={styles.toolQuery}>{toolInput}</span>
              </div>
              <div className={styles.toolCallPreview}>
                {typeof toolResult === 'string' ? (
                  toolResult.length > 100 ? `${toolResult.substring(0, 100)}...` : toolResult
                ) : (
                  `${actionName} 执行完成`
                )}
              </div>
            </div>
          );
          
          orderedElements.push(toolCallElement);
        } else if (event.event_type === EVENT_TYPES.CHAT_COMPLETION_CHUNK) {
          // 处理 CHAT_COMPLETION_CHUNK 事件类型 - 流式输出
          const { event_payload } = event;
          console.log('CHAT_COMPLETION_CHUNK event_payload', event_payload);

          const actionName = event_payload?.action_name;

          // 获取内容
          const eventContent = event_payload?.content?.tool_result || event_payload?.content || '';
          let contentString = '';
          if (typeof eventContent === 'object' && eventContent !== null && 'tool_result' in eventContent) {
            contentString = typeof eventContent.tool_result === 'string' ? eventContent.tool_result : JSON.stringify(eventContent.tool_result, null, 2);
          } else if (typeof eventContent === 'string') {
            contentString = eventContent;
          } else {
            contentString = JSON.stringify(eventContent, null, 2);
          }

          // 根据action_name处理流式内容
          if (actionName === ACTION_NAMES.REASONING_CONTENT) {
            // 流式推理内容 - 默认展开
            if (accumulatedText.trim()) {
              orderedElements.push(
                <MarkdownRenderer
                  key={`text-before-streaming-reasoning-${index}`}
                  content={accumulatedText}
                  onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                  isStreaming={isStreaming}
                  role="ai"
                />
              );
              accumulatedText = '';
            }

            const groupKey = `streaming-${actionName}-${event.query || 'default'}`;
            const groupContent = contentGroups[groupKey];
            const contentToShow = groupContent ? groupContent.accumulatedContent : contentString;

            if (groupContent && !processedGroups.has(groupKey)) {
              processedGroups.add(groupKey);

              orderedElements.push(
                <StreamingReasoningContent
                  key={`streaming-reasoning-${groupKey}`}
                  content={contentToShow}
                  isStreaming={isStreaming}
                  defaultExpanded={true} // CHAT_COMPLETION_CHUNK 默认展开
                  onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                />
              );
            }
          } else if (actionName === ACTION_NAMES.LLM_ANSWER) {
            // 流式LLM回答 - 变淡显示
            if (accumulatedText.trim()) {
              orderedElements.push(
                <MarkdownRenderer
                  key={`text-before-streaming-llm-answer-${index}`}
                  content={accumulatedText}
                  onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                  isStreaming={isStreaming}
                  role="ai"
                />
              );
              accumulatedText = '';
            }

            const groupKey = `streaming-${actionName}-${event.query || 'default'}`;
            const groupContent = contentGroups[groupKey];
            const contentToShow = groupContent ? groupContent.accumulatedContent : contentString;

            if (groupContent && !processedGroups.has(groupKey)) {
              processedGroups.add(groupKey);

              orderedElements.push(
                <StreamingLLMAnswer
                  key={`streaming-llm-answer-${groupKey}`}
                  content={contentToShow}
                  isStreaming={isStreaming}
                  onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                />
              );
            }
          } else if (actionName === ACTION_NAMES.LLM) {
            // 流式LLM内容
            if (accumulatedText.trim()) {
              orderedElements.push(
                <MarkdownRenderer
                  key={`text-before-streaming-llm-${index}`}
                  content={accumulatedText}
                  onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                  isStreaming={isStreaming}
                  role="ai"
                />
              );
              accumulatedText = '';
            }

            const groupKey = `streaming-${actionName}-${event.query || 'default'}`;
            const groupContent = contentGroups[groupKey];
            const contentToShow = groupContent ? groupContent.accumulatedContent : contentString;

            if (groupContent && !processedGroups.has(groupKey)) {
              processedGroups.add(groupKey);

              orderedElements.push(
                <StreamingLLMContent
                  key={`streaming-llm-${groupKey}`}
                  content={contentToShow}
                  isStreaming={isStreaming}
                  onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                />
              );
            }
          } else if (actionName === ACTION_NAMES.HTML || actionName === ACTION_NAMES.SVG) {
            // HTML/SVG 流式输出到虚拟机
            if (accumulatedText.trim()) {
              orderedElements.push(
                <MarkdownRenderer
                  key={`text-before-streaming-code-${index}`}
                  content={accumulatedText}
                  onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                  isStreaming={isStreaming}
                  role="ai"
                />
              );
              accumulatedText = '';
            }

            const groupKey = `streaming-${actionName}-${event.query || 'default'}`;
            const groupContent = contentGroups[groupKey];
            const contentToShow = groupContent ? groupContent.accumulatedContent : contentString;

            // 检测是否完整
            let isComplete = false;
            if (actionName === ACTION_NAMES.HTML) {
              isComplete = isHtmlStart(contentToShow) && isHtmlEnd(contentToShow);
            } else if (actionName === ACTION_NAMES.SVG) {
              isComplete = isSvgStart(contentToShow) && isSvgEnd(contentToShow);
            }

            if (groupContent && !processedGroups.has(groupKey)) {
              processedGroups.add(groupKey);

              // 获取工具图标
              const toolIcon = ACTION_NAME_ICONS[actionName as keyof typeof ACTION_NAME_ICONS] || ACTION_NAME_ICONS.default;

              // 创建代码块预览元素，点击时在虚拟机中流式显示
              const codeBlockElement = (
                <div
                  key={`streaming-code-${groupKey}`}
                  id={event.id}
                  className={`${styles.codeBlockItem} ${isStreaming ? styles.streaming : ''}`}
                  data-tool={actionName}
                  onClick={() => onToolEventClick?.(event.id, actionName, contentToShow, `${actionName.toUpperCase()} 代码`)}
                  style={{ cursor: 'pointer', marginBottom: '8px' }}
                >
                  <div className={styles.codeBlockHeader}>
                    <span className={styles.toolIcon}>{toolIcon}</span>
                    <span className={styles.actionName}>{actionName.toUpperCase()} 代码</span>
                    {isStreaming && !isComplete && (
                      <span className={styles.streamingIndicator}>流式输出中...</span>
                    )}
                    {isComplete && (
                      <span className={styles.completeIndicator}>完成</span>
                    )}
                  </div>
                  <div className={styles.codeBlockPreview}>
                    {contentToShow.length > 100 ? `${contentToShow.substring(0, 100)}...` : contentToShow}
                  </div>
                </div>
              );

              orderedElements.push(codeBlockElement);
            }
          } else {
            // 其他内容累积到文本中
            accumulatedText += contentString;
          }
        } else if (event.event_type === EVENT_TYPES.CHAT_COMPLETION) {
          // 处理 CHAT_COMPLETION 事件类型 - 静态显示
          const { event_payload } = event;
          console.log('CHAT_COMPLETION event_payload', event_payload);

          const actionName = event_payload?.action_name;
          
          // 检查是否是特殊的action类型
          if (actionName === ACTION_NAMES.HTML || actionName === ACTION_NAMES.SVG) {
            // 先添加之前累积的文本（如果有的话）
            if (accumulatedText.trim()) {
              orderedElements.push(
                <MarkdownRenderer
                  key={`text-before-code-${index}`}
                  content={accumulatedText}
                  onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                  isStreaming={isStreaming}
                  role="ai"
                />
              );
              accumulatedText = '';
            }
            
            // 获取代码内容
            const eventContent = event_payload?.content?.tool_result || event_payload?.content || '';
            let contentString = '';
            if (typeof eventContent === 'object' && eventContent !== null && 'tool_result' in eventContent) {
              contentString = typeof eventContent.tool_result === 'string' ? eventContent.tool_result : JSON.stringify(eventContent.tool_result, null, 2);
            } else if (typeof eventContent === 'string') {
              contentString = eventContent;
            } else {
              contentString = JSON.stringify(eventContent, null, 2);
            }
            
            // 获取工具图标
            const toolIcon = ACTION_NAME_ICONS[actionName as keyof typeof ACTION_NAME_ICONS] || ACTION_NAME_ICONS.default;
            
            // 创建代码块预览元素
            const codeBlockElement = (
              <div
                key={`code-${event.id}-${index}`}
                id={event.id}
                className={styles.codeBlockItem}
                data-tool={actionName}
                onClick={() => onToolEventClick?.(event.id, actionName, contentString, `${actionName.toUpperCase()} 代码`)}
                style={{ cursor: 'pointer', marginBottom: '8px' }}
              >
                <div className={styles.codeBlockHeader}>
                  <span className={styles.toolIcon}>{toolIcon}</span>
                  <span className={styles.actionName}>{actionName.toUpperCase()} 代码</span>
                </div>
                <div className={styles.codeBlockPreview}>
                  {contentString.length > 100 ? `${contentString.substring(0, 100)}...` : contentString}
                </div>
              </div>
            );
            
            orderedElements.push(codeBlockElement);
          } else {
            // 普通的CHAT_COMPLETION事件处理
            // 统一获取内容的方式：优先使用 tool_result，然后使用整个 content
            const eventContent = event_payload?.content?.tool_result || event_payload?.content || '';
            console.log('eventContent', typeof eventContent, eventContent);
            
            // 如果 eventContent 是对象且包含 tool_result 字段，则使用该字段
            let contentString = '';
            if (typeof eventContent === 'object' && eventContent !== null && 'tool_result' in eventContent) {
              contentString = typeof eventContent.tool_result === 'string' ? eventContent.tool_result : JSON.stringify(eventContent.tool_result, null, 2);
            } else if (typeof eventContent === 'string') {
              contentString = eventContent;
            } else {
              contentString = JSON.stringify(eventContent, null, 2);
            }
            
            // 根据action_name决定如何处理内容
            if (actionName === ACTION_NAMES.LLM_ANSWER) {
              // LLM_ANSWER需要变淡显示
              if (accumulatedText.trim()) {
                orderedElements.push(
                  <MarkdownRenderer
                    key={`text-before-llm-${index}`}
                    content={accumulatedText}
                    onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                    isStreaming={isStreaming}
                    role="ai"
                  />
                );
                accumulatedText = '';
              }
              
              orderedElements.push(
                <div key={`llm-answer-${index}`} className={styles.llmAnswerContent}>
                  <MarkdownRenderer
                    content={contentString}
                    onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                    isStreaming={isStreaming}
                    role="ai"
                  />
                </div>
              );
            } else if (actionName === ACTION_NAMES.REASONING_CONTENT) {
              // REASONING_CONTENT需要支持折叠功能和流式累加
              if (accumulatedText.trim()) {
                orderedElements.push(
                  <MarkdownRenderer
                    key={`text-before-reasoning-${index}`}
                    content={accumulatedText}
                    onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                    isStreaming={isStreaming}
                    role="ai"
                  />
                );
                accumulatedText = '';
              }

              const reasoningId = `reasoning-${event.id || index}`;
              const isCollapsed = !expandedItems.has(reasoningId);

              // 对于CHAT_COMPLETION事件，直接使用内容，不需要流式状态管理

              orderedElements.push(
                <div key={reasoningId} className={styles.reasoningContent}>
                  <div
                    className={styles.reasoningHeader}
                    onClick={() => toggleCollapse(reasoningId)}
                  >
                    <span className={styles.reasoningIcon}>
                      {isCollapsed ? <RightOutlined /> : <DownOutlined />}
                    </span>
                    <span className={styles.reasoningTitle}>
                      🧠 推理过程
                    </span>
                  </div>
                  {!isCollapsed && (
                    <div className={styles.reasoningBody}>
                      <MarkdownRenderer
                        content={contentString}
                        onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                        isStreaming={isStreaming}
                        role="ai"
                      />
                    </div>
                  )}
                </div>
              );
            } else {
              // 累积到文本中
              accumulatedText += contentString;
            }
          }
        }
      });
      
      // 处理最后剩余的文本
      if (accumulatedText.trim()) {
        orderedElements.push(
          <MarkdownRenderer
            key="final-text"
            content={accumulatedText}
            onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
            isStreaming={isStreaming}
            role="ai"
          />
        );
      }
      
      setOrderedContent(orderedElements);
    } else if (typeof content === 'string') {
      // 处理字符串content
      
      // 尝试解析多个JSON事件（流式返回时可能有多个JSON对象拼接）
      const jsonObjects = [];
      let currentJson = '';
      let braceCount = 0;
      let inString = false;
      let escapeNext = false;
      
      for (let i = 0; i < content.length; i++) {
        const char = content[i];
        currentJson += char;
        
        if (escapeNext) {
          escapeNext = false;
          continue;
        }
        
        if (char === '\\') {
          escapeNext = true;
          continue;
        }
        
        if (char === '"') {
          inString = !inString;
          continue;
        }
        
        if (!inString) {
          if (char === '{') {
            braceCount++;
          } else if (char === '}') {
            braceCount--;
            if (braceCount === 0) {
              // 完整的JSON对象
              try {
                const jsonObj = JSON.parse(currentJson.trim());
                jsonObjects.push(jsonObj);
                currentJson = '';
              } catch (e) {
                // 忽略解析错误，继续处理
              }
            }
          }
        }
      }
      
      // 如果有解析成功的JSON对象，按照事件数组的方式处理
      if (jsonObjects.length > 0) {
        const orderedElements: JSX.Element[] = [];
        let accumulatedText = '';
        
        jsonObjects.forEach((dbEvent: any, index: number) => {
          if (dbEvent.event_type === EVENT_TYPES.TOOL_CALL) {
            // 先添加之前累积的文本（如果有的话）
            if (accumulatedText.trim()) {
              orderedElements.push(
                <MarkdownRenderer
                  key={`json-text-before-${index}`}
                  content={accumulatedText}
                  onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                  isStreaming={isStreaming}
                  role="ai"
                />
              );
              accumulatedText = '';
            }
            
            // 处理工具调用事件
            const { id, query, event_payload } = dbEvent;
            const actionName = event_payload?.action_name || 'unknown';
            const toolInput = event_payload?.content?.input || query;
            const toolResult = event_payload?.content?.tool_result || '';
            
            // 检查是否应该显示虚拟机
            const isShow = event_payload?.is_show !== false; // 默认为true，只有明确设置为false才不显示
            const hasContent = event_payload?.content && Object.keys(event_payload.content).length > 0;
            const canClickToVirtualMachine = isShow && hasContent;
            
            // 获取工具图标
            const toolIcon = ACTION_NAME_ICONS[actionName as keyof typeof ACTION_NAME_ICONS] || ACTION_NAME_ICONS.default;
            
            // 创建工具调用元素
            const toolCallElement = (
              <div
                key={`json-tool-${id}-${index}`}
                id={id}
                className={`${styles.toolCallItem} ${!canClickToVirtualMachine ? styles.toolCallItemDisabled : ''}`}
                data-tool={actionName}
                onClick={() => canClickToVirtualMachine && onToolEventClick?.(id, actionName, toolResult, toolInput)}
                style={{ cursor: canClickToVirtualMachine ? 'pointer' : 'default', marginBottom: '8px' }}
              >
                <div className={styles.toolCallHeader}>
                  <span className={styles.toolIcon}>{toolIcon}</span>
                  <span className={styles.actionName}>{actionName}</span>
                  <span className={styles.toolQuery}>{query}</span>
                </div>
                <div className={styles.toolCallPreview}>
                  {typeof toolResult === 'string' ? (
                    toolResult.length > 100 ? `${toolResult.substring(0, 100)}...` : toolResult
                  ) : (
                    `${actionName} 执行完成`
                  )}
                </div>
              </div>
            );
            
            orderedElements.push(toolCallElement);
          } else if (
            dbEvent.event_type === EVENT_TYPES.CHAT_COMPLETION ||
            dbEvent.event_type === EVENT_TYPES.CHAT_COMPLETION_CHUNK
          ) {
            // 处理 CHAT_COMPLETION 和 CHAT_COMPLETION_CHUNK 事件类型 - 统一处理逻辑
            const { event_payload } = dbEvent;
            console.log('event_payload', event_payload);
            
            const actionName = event_payload?.action_name;
            
            // 检查是否是特殊的action类型
            if (actionName === ACTION_NAMES.HTML || actionName === ACTION_NAMES.SVG) {
              // 先添加之前累积的文本（如果有的话）
              if (accumulatedText.trim()) {
                orderedElements.push(
                  <MarkdownRenderer
                    key={`json-text-before-code-${index}`}
                    content={accumulatedText}
                    onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                    isStreaming={isStreaming}
                    role="ai"
                  />
                );
                accumulatedText = '';
              }
              
              // 获取代码内容
              const eventContent = event_payload?.content?.tool_result || event_payload?.content || '';
              let contentString = '';
              if (typeof eventContent === 'object' && eventContent !== null && 'tool_result' in eventContent) {
                contentString = typeof eventContent.tool_result === 'string' ? eventContent.tool_result : JSON.stringify(eventContent.tool_result, null, 2);
              } else if (typeof eventContent === 'string') {
                contentString = eventContent;
              } else {
                contentString = JSON.stringify(eventContent, null, 2);
              }
              
              // 获取工具图标
              const toolIcon = ACTION_NAME_ICONS[actionName as keyof typeof ACTION_NAME_ICONS] || ACTION_NAME_ICONS.default;
              
              // 创建代码块预览元素
              const codeBlockElement = (
                <div
                  key={`json-code-${dbEvent.id}-${index}`}
                  id={dbEvent.id}
                  className={styles.codeBlockItem}
                  data-tool={actionName}
                  onClick={() => onToolEventClick?.(dbEvent.id, actionName, contentString, `${actionName.toUpperCase()} 代码`)}
                  style={{ cursor: 'pointer', marginBottom: '8px' }}
                >
                  <div className={styles.codeBlockHeader}>
                    <span className={styles.toolIcon}>{toolIcon}</span>
                    <span className={styles.actionName}>{actionName.toUpperCase()} 代码</span>
                  </div>
                  <div className={styles.codeBlockPreview}>
                    {contentString.length > 100 ? `${contentString.substring(0, 100)}...` : contentString}
                  </div>
                </div>
              );
              
              orderedElements.push(codeBlockElement);
            } else {
              // 普通的CHAT_COMPLETION事件处理
              // 统一获取内容的方式：优先使用 tool_result，然后使用整个 content
              const eventContent = event_payload?.content?.tool_result || event_payload?.content || '';
              // console.log('eventContent', typeof eventContent, eventContent);
              
              // 如果 eventContent 是对象且包含 tool_result 字段，则使用该字段
              let contentString = '';
              if (typeof eventContent === 'object' && eventContent !== null && 'tool_result' in eventContent) {
                contentString = typeof eventContent.tool_result === 'string' ? eventContent.tool_result : JSON.stringify(eventContent.tool_result, null, 2);
              } else if (typeof eventContent === 'string') {
                contentString = eventContent;
              } else {
                contentString = JSON.stringify(eventContent, null, 2);
              }
              
              // 根据action_name决定如何处理内容
              if (actionName === ACTION_NAMES.LLM_ANSWER) {
                // LLM_ANSWER需要变淡显示
                if (accumulatedText.trim()) {
                  orderedElements.push(
                    <MarkdownRenderer
                      key={`json-text-before-llm-${index}`}
                      content={accumulatedText}
                      onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                      isStreaming={isStreaming}
                      role="ai"
                    />
                  );
                  accumulatedText = '';
                }
                
                orderedElements.push(
                  <div key={`json-llm-answer-${index}`} className={styles.llmAnswerContent}>
                    <MarkdownRenderer
                      content={contentString}
                      onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                      isStreaming={isStreaming}
                      role="ai"
                    />
                  </div>
                );
              } else if (actionName === ACTION_NAMES.REASONING_CONTENT) {
                // REASONING_CONTENT需要支持折叠功能
                if (accumulatedText.trim()) {
                  orderedElements.push(
                    <MarkdownRenderer
                      key={`json-text-before-reasoning-${index}`}
                      content={accumulatedText}
                      onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                      isStreaming={isStreaming}
                      role="ai"
                    />
                  );
                  accumulatedText = '';
                }
                
                const reasoningId = `json-reasoning-${dbEvent.id || index}`;
                const isCollapsed = !expandedItems.has(reasoningId);
                
                orderedElements.push(
                  <div key={reasoningId} className={styles.reasoningContent}>
                    <div 
                      className={styles.reasoningHeader}
                      onClick={() => toggleCollapse(reasoningId)}
                    >
                      <span className={styles.reasoningIcon}>
                        {isCollapsed ? <RightOutlined /> : <DownOutlined />}
                      </span>
                      <span className={styles.reasoningTitle}>
                        🧠 推理过程
                      </span>
                    </div>
                    {!isCollapsed && (
                      <div className={styles.reasoningBody}>
                        <MarkdownRenderer
                          content={contentString}
                          onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
                          isStreaming={isStreaming}
                          role="ai"
                        />
                      </div>
                    )}
                  </div>
                );
              } else {
                // 累积到文本中
                accumulatedText += contentString;
              }
            }
          }
        });
        
        // 处理最后剩余的文本
        if (accumulatedText.trim()) {
          orderedElements.push(
            <MarkdownRenderer
              key="json-final-text"
              content={accumulatedText}
              onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
              isStreaming={isStreaming}
              role="ai"
            />
          );
        }
        
        setOrderedContent(orderedElements);
      } else {
        // 不是JSON格式，直接作为普通文本处理
        setOrderedContent([
          <MarkdownRenderer
            key="simple-text"
            content={content}
            onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
            isStreaming={isStreaming}
            role="ai"
          />
        ]);
      }
    } else {
      // 其他类型，直接显示
      setOrderedContent([
        <MarkdownRenderer
          key="other-content"
          content={String(content)}
          onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
          isStreaming={isStreaming}
          role="ai"
        />
      ]);
    }
  };

  if (role === 'local') {
    return (
      <div className={styles.messageProcessor}>
        {orderedContent}
      </div>
    );
  }

  return (
    <div className={styles.messageProcessor}>
      {orderedContent}
    </div>
  );
};

export default MessageProcessor; 