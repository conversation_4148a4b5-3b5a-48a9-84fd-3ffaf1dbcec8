.messageProcessor {
  width: 100%;
}

.toolCallsContainer {
  margin-bottom: 16px;
}

.toolCallItem {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #e9ecef;
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.toolCallHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  
  .toolIcon {
    font-size: 16px;
    flex-shrink: 0;
  }
  
  .actionName {
    background: #1890ff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
  }
  
  .toolQuery {
    color: #666;
    font-size: 14px;
    font-weight: 500;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.toolCallPreview {
  color: #8c8c8c;
  font-size: 13px;
  line-height: 1.4;
  padding-left: 24px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 不同工具类型的样式变化 */
.toolCallItem[data-tool="search"] {
  .toolCallHeader .actionName {
    background: #52c41a;
  }
  
  &:hover {
    border-color: #52c41a;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
  }
}

.toolCallItem[data-tool="view"] {
  .toolCallHeader .actionName {
    background: #fa8c16;
  }
  
  &:hover {
    border-color: #fa8c16;
    box-shadow: 0 2px 8px rgba(250, 140, 22, 0.15);
  }
}

.toolCallItem[data-tool="mcp"] {
  .toolCallHeader .actionName {
    background: #722ed1;
  }
  
  &:hover {
    border-color: #722ed1;
    box-shadow: 0 2px 8px rgba(114, 46, 209, 0.15);
  }
}

.toolCallItem[data-tool="query_expansion"] {
  .toolCallHeader .actionName {
    background: #eb2f96;
  }
  
  &:hover {
    border-color: #eb2f96;
    box-shadow: 0 2px 8px rgba(235, 47, 150, 0.15);
  }
}

.toolCallItem[data-tool="summary"] {
  .toolCallHeader .actionName {
    background: #13c2c2;
  }
  
  &:hover {
    border-color: #13c2c2;
    box-shadow: 0 2px 8px rgba(19, 194, 194, 0.15);
  }
}

.toolCallItem[data-tool="deduplication"] {
  .toolCallHeader .actionName {
    background: #faad14;
  }
  
  &:hover {
    border-color: #faad14;
    box-shadow: 0 2px 8px rgba(250, 173, 20, 0.15);
  }
}

.toolCallItem[data-tool="aggregation"] {
  .toolCallHeader .actionName {
    background: #1890ff;
  }
  
  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  }
}

.toolCallItem[data-tool="reflection"] {
  .toolCallHeader .actionName {
    background: #fa8c16;
  }
  
  &:hover {
    border-color: #fa8c16;
    box-shadow: 0 2px 8px rgba(250, 140, 22, 0.15);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolCallItem {
    padding: 8px;
  }
  
  .toolCallHeader {
    flex-wrap: wrap;
    gap: 6px;
    
    .toolQuery {
      flex-basis: 100%;
      white-space: normal;
      overflow: visible;
      text-overflow: unset;
      margin-top: 4px;
    }
  }
  
  .toolCallPreview {
    padding-left: 16px;
    font-size: 12px;
  }
}

.toolCallItemDisabled {
  opacity: 0.6;
  background: #f5f5f5 !important;
  
  &:hover {
    background: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    box-shadow: none !important;
  }
  
  .toolCallHeader .actionName {
    background: #8c8c8c !important;
  }
}

/* LLM_ANSWER 内容变淡样式 */
.llmAnswerContent {
  opacity: 0.7;
  color: #8c8c8c;
}

/* REASONING_CONTENT 推理内容样式 */
.reasoningContent {
  margin: 12px 0;
  border: 1px solid #e0e7ff;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  opacity: 0.8;
  transition: all 0.3s ease;
}

.reasoningContent:hover {
  opacity: 1;
  border-color: #c7d2fe;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
}

.reasoningHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(99, 102, 241, 0.05);
  border-bottom: 1px solid #e0e7ff;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.reasoningHeader:hover {
  background: rgba(99, 102, 241, 0.1);
}

.reasoningIcon {
  display: flex;
  align-items: center;
  color: #6366f1;
  font-size: 14px;
  transition: transform 0.2s ease;
}

.reasoningTitle {
  color: #6366f1;
  font-weight: 500;
  font-size: 14px;
  flex: 1;
}



.reasoningBody {
  padding: 16px;
  background: #ffffff;
  border-radius: 0 0 8px 8px;
  color: #475569;
}

.reasoningBody :global(.markdown-body) {
  background: transparent;
  color: #475569;
  font-size: 14px;
  line-height: 1.6;
}

.reasoningBody :global(.markdown-body h1),
.reasoningBody :global(.markdown-body h2),
.reasoningBody :global(.markdown-body h3),
.reasoningBody :global(.markdown-body h4),
.reasoningBody :global(.markdown-body h5),
.reasoningBody :global(.markdown-body h6) {
  color: #374151;
}

.reasoningBody :global(.markdown-body code) {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.reasoningBody :global(.markdown-body pre) {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

/* 代码块样式 */
.codeBlockItem {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    border-color: #adb5bd;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  }
}

.codeBlockHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  
  .toolIcon {
    font-size: 18px;
    color: #495057;
    flex-shrink: 0;
  }
  
  .actionName {
    background: #e9ecef;
    color: #495057;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 13px;
    font-weight: 600;
    flex-shrink: 0;
    border: 1px solid #ced4da;
  }
  
  .codeBlockTag {
    background: #f8f9fa;
    color: #6c757d;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    flex: 1;
    text-align: right;
    border: 1px solid #e9ecef;
  }
}

.codeBlockPreview {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  color: #495057;
  font-size: 13px;
  line-height: 1.5;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* HTML 代码块特殊样式 */
.codeBlockItem[data-tool="html"] {
  background: linear-gradient(135deg, #fff5f5 0%, #ffeaea 100%);
  border-color: #ff9999;
}

.codeBlockItem[data-tool="html"]:hover {
  background: linear-gradient(135deg, #ffffff 0%, #fff5f5 100%);
  border-color: #ff7777;
}

.codeBlockItem[data-tool="html"] .codeBlockHeader .actionName {
  background: #ff9999;
  color: #ffffff;
}

/* SVG 代码块特殊样式 */
.codeBlockItem[data-tool="svg"] {
  background: linear-gradient(135deg, #f0ffff 0%, #e0f7fa 100%);
  border-color: #4ecdc4;
}

.codeBlockItem[data-tool="svg"]:hover {
  background: linear-gradient(135deg, #ffffff 0%, #f0ffff 100%);
  border-color: #26a69a;
}

.codeBlockItem[data-tool="svg"] .codeBlockHeader .actionName {
  background: #4ecdc4;
  color: #ffffff;
}



.disabledIndicator {
  background: #ff4d4f;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  flex-shrink: 0;
  margin-left: auto;
}

/* 流式输出指示器 */
.streamingIndicator {
  background: #52c41a;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  flex-shrink: 0;
  margin-left: auto;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
  from {
    opacity: 0.6;
  }
  to {
    opacity: 1;
  }
}

/* LLM 内容样式 */
.llmContent {
  /* 正常显示，无特殊样式 */
}

/* 流式输出状态下的特殊样式 */
.reasoningContent.streaming {
  border-color: #52c41a;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
}

.llmAnswerContent.streaming {
  opacity: 0.8;
  border-left: 3px solid #52c41a;
  padding-left: 12px;
}

.codeBlockItem.streaming {
  border-color: #52c41a;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
}

.codeBlockItem.streaming .codeBlockHeader {
  background: rgba(82, 196, 26, 0.05);
}