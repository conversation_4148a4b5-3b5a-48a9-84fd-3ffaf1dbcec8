import React, { useState, useEffect } from 'react';
import MarkdownRenderer from './MarkdownRenderer';
import styles from './MessageProcessor.module.css';

interface StreamingLLMContentProps {
  content: string;
  isStreaming?: boolean;
  onHtmlSvgCodeDetected?: (detectedBlocks: any[], isUserClick?: boolean) => void;
}

const StreamingLLMContent: React.FC<StreamingLLMContentProps> = ({
  content,
  isStreaming = false,
  onHtmlSvgCodeDetected
}) => {
  const [accumulatedContent, setAccumulatedContent] = useState('');

  // 直接使用传入的内容，不需要累加（累加在父组件中已经完成）
  useEffect(() => {
    setAccumulatedContent(content);
  }, [content]);

  return (
    <div className={`${styles.llmContent} ${isStreaming ? styles.streaming : ''}`}>
      <MarkdownRenderer
        content={accumulatedContent}
        onHtmlSvgCodeDetected={onHtmlSvgCodeDetected}
        isStreaming={isStreaming}
        role="ai"
      />
      {isStreaming && (
        <span className={styles.streamingIndicator}>流式输出中...</span>
      )}
    </div>
  );
};

export default StreamingLLMContent;
